<style>
    .resume-container * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    .resume-container {
        font-family: '<PERSON><PERSON><PERSON>', 'Arial', sans-serif;
        font-size: 10pt;
        line-height: 1.4;
        color: #2d3748;
        background-color: white;
        width: 210mm;
        height: 297mm;
        margin: 0 auto;
        padding: 12mm 16mm;
        page-break-inside: avoid;
        box-sizing: border-box;
        overflow: hidden;
    }

    /* Header Section */
    .resume-container .header {
        text-align: left;
        margin-bottom: 24px;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 8px;
    }

    .resume-container .name {
        font-size: 20pt;
        font-weight: 700;
        color: white;
        margin-bottom: 4px;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    }

    .resume-container .title {
        font-size: 12pt;
        color: #f7fafc;
        margin-bottom: 8px;
        font-weight: 400;
    }

    .resume-container .contact-info {
        font-size: 9pt;
        color: #f7fafc;
        line-height: 1.4;
    }

    /* Section Styling */
    .resume-container .section {
        margin-bottom: 20px;
    }

    .resume-container .section-title {
        font-size: 11pt;
        font-weight: 600;
        color: #667eea;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 12px;
        padding: 8px 0 8px 15px;
        background-color: #f7fafc;
        border-left: 4px solid #667eea;
        border-radius: 0 4px 4px 0;
    }

    /* Experience and Education */
    .resume-container .experience-item,
    .resume-container .education-item {
        margin-bottom: 16px;
        padding: 12px;
        background-color: #fafafa;
        border-radius: 6px;
        border-left: 3px solid #e2e8f0;
    }

    .resume-container .job-title,
    .resume-container .degree {
        font-size: 10pt;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 3px;
    }

    .resume-container .company,
    .resume-container .school {
        font-size: 10pt;
        color: #667eea;
        font-weight: 500;
        margin-bottom: 3px;
    }

    .resume-container .date {
        font-size: 9pt;
        color: #718096;
        margin-bottom: 6px;
        font-weight: 400;
    }

    .resume-container .description {
        font-size: 10pt;
        color: #2d3748;
        margin-left: 0;
    }

    .resume-container .description ul {
        margin-left: 18px;
        margin-top: 4px;
    }

    .resume-container .description li {
        margin-bottom: 3px;
        list-style-type: disc;
    }

    /* Skills Section */
    .resume-container .skills-list {
        font-size: 10pt;
        color: #2d3748;
    }

    .resume-container .skill-category {
        margin-bottom: 10px;
        line-height: 1.5;
        padding: 10px;
        background-color: #f7fafc;
        border-radius: 6px;
        border-left: 3px solid #667eea;
    }

    .resume-container .skill-category strong {
        font-weight: 600;
        color: #667eea;
    }

    /* Summary */
    .resume-container .summary {
        font-size: 10pt;
        color: #2d3748;
        line-height: 1.5;
        text-align: left;
        padding: 15px;
        background-color: #edf2f7;
        border-radius: 6px;
        border-left: 4px solid #667eea;
    }

    /* Projects */
    .resume-container .project-item {
        margin-bottom: 12px;
        padding: 12px;
        background-color: #f7fafc;
        border-radius: 6px;
        border: 1px solid #e2e8f0;
    }

    .resume-container .project-title {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 3px;
    }

    .resume-container .project-description {
        color: #4a5568;
        margin-bottom: 2px;
    }

    .resume-container .project-tech {
        font-size: 9pt;
        color: #718096;
        font-style: italic;
    }

    /* Certifications */
    .resume-container .certification-item {
        margin-bottom: 6px;
        font-size: 10pt;
        color: #2d3748;
        padding: 8px 12px;
        background-color: #f7fafc;
        border-radius: 4px;
        border-left: 3px solid #667eea;
    }

    /* Languages and Awards */
    .resume-container .language-item,
    .resume-container .award-item {
        margin-bottom: 6px;
        font-size: 10pt;
        color: #2d3748;
        padding: 6px 12px;
        background-color: #f7fafc;
        border-radius: 4px;
    }

    /* Print styles for A4 */
    @media print {
        .resume-container {
            width: 210mm !important;
            height: 297mm !important;
            margin: 0 !important;
            padding: 12mm 16mm !important;
            page-break-inside: avoid !important;
            box-sizing: border-box !important;
        }
        
        .resume-container .header {
            background: #667eea !important;
            -webkit-print-color-adjust: exact;
            color-adjust: exact;
        }
    }

    /* PDF generation styles */
    @page {
        size: A4;
        margin: 0;
    }
</style>
<div class="resume-container">
    <!-- Header -->
    <div class="header">
        <div class="name">{{name}}</div>
        <div class="title">{{jobTitle}}</div>
        <div class="contact-info">
            {{joinContact email phone linkedin website github location ' • '}}
        </div>
    </div>

    <!-- Professional Summary -->
    {{#if summary}}
    <div class="section">
        <div class="section-title">Ringkasan Profesional</div>
        <div class="summary">
            {{summary}}
        </div>
    </div>
    {{/if}}

    <!-- Experience -->
    {{#if experiences}}
    <div class="section">
        <div class="section-title">Pengalaman Profesional</div>
        {{#each experiences}}
        <div class="experience-item">
            <div class="job-title">{{jobTitle}}</div>
            <div class="company">{{company}}</div>
            <div class="date">{{startDate}} - {{endDate}}</div>
            {{#if responsibilities}}
            <div class="description">
                <ul>
                    {{#each responsibilities}}
                    {{#if (hasContent this)}}<li>{{this}}</li>{{/if}}
                    {{/each}}
                </ul>
            </div>
            {{/if}}
        </div>
        {{/each}}
    </div>
    {{/if}}

    <!-- Education -->
    {{#if education}}
    <div class="section">
        <div class="section-title">Pendidikan</div>
        {{#each education}}
        <div class="education-item">
            <div class="degree">{{degree}}</div>
            <div class="school">{{institution}}</div>
            {{#if location}}<div class="school">{{location}}</div>{{/if}}
            <div class="date">{{graduationDate}}</div>
            {{#if gpa}}<div style="font-size: 9pt; color: #718096; margin-top: 2px;">IPK: {{gpa}}</div>{{/if}}
            {{#if relevantCoursework}}
            <div style="margin-top: 3px; font-size: 9pt; color: #4a5568;">
                <strong>Mata Kuliah Terkait:</strong> {{relevantCoursework}}
            </div>
            {{/if}}
            {{#if honors}}
            <div style="margin-top: 2px; font-size: 9pt; color: #4a5568;">
                <strong>Penghargaan:</strong> {{honors}}
            </div>
            {{/if}}
        </div>
        {{/each}}
    </div>
    {{/if}}

    <!-- Skills -->
    {{#if skills}}
    <div class="section">
        <div class="section-title">Keahlian</div>
        <div class="skills-list">
            {{#each skills}}
            {{#if (hasContent skills)}}
            <div class="skill-category">
                {{#if (hasContent category)}}<strong>{{category}}:</strong>{{/if}} {{skills}}
            </div>
            {{/if}}
            {{/each}}
        </div>
    </div>
    {{/if}}

    <!-- Projects -->
    {{#if projects}}
    <div class="section">
        <div class="section-title">Proyek Utama</div>
        {{#each projects}}
        <div class="project-item">
            <div class="project-title">{{title}}</div>
            {{#if link}}<div style="font-size: 9pt; color: #667eea; margin-bottom: 2px;">{{link}}</div>{{/if}}
            {{#if description}}<div class="project-description">{{description}}</div>{{/if}}
            {{#if technologies}}<div class="project-tech">Teknologi: {{technologies}}</div>{{/if}}
            {{#if achievements}}
            <ul style="margin-left: 18px; margin-top: 3px; font-size: 10pt; color: #2d3748;">
                {{#each achievements}}
                {{#if (hasContent this)}}<li>{{this}}</li>{{/if}}
                {{/each}}
            </ul>
            {{/if}}
        </div>
        {{/each}}
    </div>
    {{/if}}

    <!-- Certifications -->
    {{#if certifications}}
    <div class="section">
        <div class="section-title">Sertifikasi</div>
        <div>
            {{#each certifications}}
            <div class="certification-item">
                {{name}} - {{issuer}} ({{date}})
                {{#if credentialId}}<div style="font-size: 9pt; color: #718096; margin-top: 1px;">ID: {{credentialId}}</div>{{/if}}
            </div>
            {{/each}}
        </div>
    </div>
    {{/if}}

    <!-- Languages -->
    {{#if languages}}
    <div class="section">
        <div class="section-title">Bahasa</div>
        <div>
            {{#each languages}}
            {{#if language}}
            <div class="language-item">
                {{language}}{{#if proficiency}} - {{proficiency}}{{/if}}
            </div>
            {{/if}}
            {{/each}}
        </div>
    </div>
    {{/if}}

    <!-- Awards -->
    {{#if awards}}
    <div class="section">
        <div class="section-title">Penghargaan</div>
        <div>
            {{#each awards}}
            <div class="award-item">
                <div style="font-weight: 600; color: #2d3748;">{{title}} - {{issuer}} ({{date}})</div>
                {{#if description}}<div style="font-size: 9pt; color: #4a5568; margin-top: 2px;">{{description}}</div>{{/if}}
            </div>
            {{/each}}
        </div>
    </div>
    {{/if}}
</div>
