<style>
    .resume-container * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    .resume-container {
        font-family: 'Times New Roman', serif;
        font-size: 10pt;
        line-height: 1.4;
        color: #1a1a1a;
        background-color: white;
        width: 210mm;
        height: 297mm;
        margin: 0 auto;
        padding: 12mm 18mm;
        page-break-inside: avoid;
        box-sizing: border-box;
        overflow: hidden;
    }

    /* Header Section */
    .resume-container .header {
        text-align: center;
        margin-bottom: 22px;
        padding: 15px 0;
        border-top: 3px solid #1a1a1a;
        border-bottom: 1px solid #1a1a1a;
    }

    .resume-container .name {
        font-size: 18pt;
        font-weight: bold;
        color: #1a1a1a;
        text-transform: uppercase;
        letter-spacing: 2px;
        margin-bottom: 6px;
    }

    .resume-container .title {
        font-size: 11pt;
        color: #333333;
        margin-bottom: 8px;
        font-style: italic;
    }

    .resume-container .contact-info {
        font-size: 9pt;
        color: #333333;
        line-height: 1.3;
    }

    /* Section Styling */
    .resume-container .section {
        margin-bottom: 18px;
    }

    .resume-container .section-title {
        font-size: 11pt;
        font-weight: bold;
        color: #1a1a1a;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 10px;
        padding: 5px 0;
        border-top: 2px solid #1a1a1a;
        border-bottom: 1px solid #cccccc;
        text-align: center;
    }

    /* Experience and Education */
    .resume-container .experience-item,
    .resume-container .education-item {
        margin-bottom: 14px;
        padding-bottom: 8px;
        border-bottom: 1px dotted #e0e0e0;
    }

    .resume-container .experience-item:last-child,
    .resume-container .education-item:last-child {
        border-bottom: none;
    }

    .resume-container .job-title,
    .resume-container .degree {
        font-size: 10pt;
        font-weight: bold;
        color: #1a1a1a;
        margin-bottom: 2px;
        text-transform: uppercase;
    }

    .resume-container .company,
    .resume-container .school {
        font-size: 10pt;
        color: #333333;
        font-weight: bold;
        margin-bottom: 3px;
    }

    .resume-container .date {
        font-size: 9pt;
        color: #666666;
        margin-bottom: 5px;
        font-style: italic;
    }

    .resume-container .description {
        font-size: 10pt;
        color: #1a1a1a;
        margin-left: 0;
        text-align: justify;
    }

    .resume-container .description ul {
        margin-left: 18px;
        margin-top: 4px;
    }

    .resume-container .description li {
        margin-bottom: 2px;
        list-style-type: disc;
    }

    /* Skills Section */
    .resume-container .skills-list {
        font-size: 10pt;
        color: #1a1a1a;
        text-align: center;
    }

    .resume-container .skill-category {
        margin-bottom: 8px;
        line-height: 1.4;
        padding: 5px 10px;
        background-color: #f8f8f8;
        border-left: 3px solid #1a1a1a;
    }

    .resume-container .skill-category strong {
        font-weight: bold;
        color: #1a1a1a;
        text-transform: uppercase;
    }

    /* Summary */
    .resume-container .summary {
        font-size: 10pt;
        color: #1a1a1a;
        line-height: 1.5;
        text-align: justify;
        font-style: italic;
        padding: 10px;
        background-color: #f9f9f9;
        border-left: 4px solid #1a1a1a;
    }

    /* Projects */
    .resume-container .project-item {
        margin-bottom: 10px;
        padding: 8px;
        background-color: #fafafa;
        border: 1px solid #e0e0e0;
    }

    .resume-container .project-title {
        font-weight: bold;
        color: #1a1a1a;
        margin-bottom: 3px;
        text-transform: uppercase;
    }

    .resume-container .project-description {
        color: #333333;
        margin-bottom: 2px;
    }

    .resume-container .project-tech {
        font-size: 9pt;
        color: #666666;
        font-style: italic;
    }

    /* Certifications */
    .resume-container .certification-item {
        margin-bottom: 5px;
        font-size: 10pt;
        color: #1a1a1a;
        padding-left: 15px;
        position: relative;
    }

    .resume-container .certification-item:before {
        content: "▪";
        position: absolute;
        left: 0;
        color: #1a1a1a;
        font-weight: bold;
    }

    /* Print styles for A4 */
    @media print {
        .resume-container {
            width: 210mm !important;
            height: 297mm !important;
            margin: 0 !important;
            padding: 12mm 18mm !important;
            page-break-inside: avoid !important;
            box-sizing: border-box !important;
        }
    }

    /* PDF generation styles */
    @page {
        size: A4;
        margin: 0;
    }
</style>
<div class="resume-container">
    <!-- Header -->
    <div class="header">
        <div class="name">{{name}}</div>
        <div class="title">{{jobTitle}}</div>
        <div class="contact-info">
            {{joinContact email phone linkedin website github location ' | '}}
        </div>
    </div>

    <!-- Professional Summary -->
    {{#if summary}}
    <div class="section">
        <div class="section-title">Ringkasan Profesional</div>
        <div class="summary">
            {{summary}}
        </div>
    </div>
    {{/if}}

    <!-- Experience -->
    {{#if experiences}}
    <div class="section">
        <div class="section-title">Pengalaman Profesional</div>
        {{#each experiences}}
        <div class="experience-item">
            <div class="job-title">{{jobTitle}}</div>
            <div class="company">{{company}}</div>
            <div class="date">{{startDate}} - {{endDate}}</div>
            {{#if responsibilities}}
            <div class="description">
                <ul>
                    {{#each responsibilities}}
                    {{#if (hasContent this)}}<li>{{this}}</li>{{/if}}
                    {{/each}}
                </ul>
            </div>
            {{/if}}
        </div>
        {{/each}}
    </div>
    {{/if}}

    <!-- Education -->
    {{#if education}}
    <div class="section">
        <div class="section-title">Pendidikan</div>
        {{#each education}}
        <div class="education-item">
            <div class="degree">{{degree}}</div>
            <div class="school">{{institution}}</div>
            {{#if location}}<div class="school">{{location}}</div>{{/if}}
            <div class="date">{{graduationDate}}</div>
            {{#if gpa}}<div style="font-size: 9pt; color: #666666; margin-top: 2px;">IPK: {{gpa}}</div>{{/if}}
            {{#if relevantCoursework}}
            <div style="margin-top: 3px; font-size: 9pt; color: #333333;">
                <strong>Mata Kuliah Terkait:</strong> {{relevantCoursework}}
            </div>
            {{/if}}
            {{#if honors}}
            <div style="margin-top: 2px; font-size: 9pt; color: #333333;">
                <strong>Penghargaan:</strong> {{honors}}
            </div>
            {{/if}}
        </div>
        {{/each}}
    </div>
    {{/if}}

    <!-- Skills -->
    {{#if skills}}
    <div class="section">
        <div class="section-title">Keahlian</div>
        <div class="skills-list">
            {{#each skills}}
            {{#if (hasContent skills)}}
            <div class="skill-category">
                {{#if (hasContent category)}}<strong>{{category}}:</strong>{{/if}} {{skills}}
            </div>
            {{/if}}
            {{/each}}
        </div>
    </div>
    {{/if}}

    <!-- Projects -->
    {{#if projects}}
    <div class="section">
        <div class="section-title">Proyek Utama</div>
        {{#each projects}}
        <div class="project-item">
            <div class="project-title">{{title}}</div>
            {{#if link}}<div style="font-size: 9pt; color: #666666; margin-bottom: 2px;">{{link}}</div>{{/if}}
            {{#if description}}<div class="project-description">{{description}}</div>{{/if}}
            {{#if technologies}}<div class="project-tech">Teknologi: {{technologies}}</div>{{/if}}
            {{#if achievements}}
            <ul style="margin-left: 18px; margin-top: 3px; font-size: 10pt; color: #1a1a1a;">
                {{#each achievements}}
                {{#if (hasContent this)}}<li>{{this}}</li>{{/if}}
                {{/each}}
            </ul>
            {{/if}}
        </div>
        {{/each}}
    </div>
    {{/if}}

    <!-- Certifications -->
    {{#if certifications}}
    <div class="section">
        <div class="section-title">Sertifikasi</div>
        <div>
            {{#each certifications}}
            <div class="certification-item">
                {{name}} - {{issuer}} ({{date}})
                {{#if credentialId}}<div style="font-size: 9pt; color: #666666; margin-top: 1px;">ID: {{credentialId}}</div>{{/if}}
            </div>
            {{/each}}
        </div>
    </div>
    {{/if}}

    <!-- Languages -->
    {{#if languages}}
    <div class="section">
        <div class="section-title">Bahasa</div>
        <div>
            {{#each languages}}
            {{#if language}}
            <div class="certification-item">
                {{language}}{{#if proficiency}} - {{proficiency}}{{/if}}
            </div>
            {{/if}}
            {{/each}}
        </div>
    </div>
    {{/if}}

    <!-- Awards -->
    {{#if awards}}
    <div class="section">
        <div class="section-title">Penghargaan</div>
        <div>
            {{#each awards}}
            <div class="certification-item">
                <div style="font-weight: bold; color: #1a1a1a;">{{title}} - {{issuer}} ({{date}})</div>
                {{#if description}}<div style="font-size: 9pt; color: #333333; margin-top: 2px;">{{description}}</div>{{/if}}
            </div>
            {{/each}}
        </div>
    </div>
    {{/if}}
</div>
