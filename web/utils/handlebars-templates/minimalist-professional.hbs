<style>
    .resume-container * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    .resume-container {
        font-family: 'Helvetica', 'Arial', sans-serif;
        font-size: 10pt;
        line-height: 1.5;
        color: #2c2c2c;
        background-color: white;
        width: 210mm;
        height: 297mm;
        margin: 0 auto;
        padding: 15mm 20mm;
        page-break-inside: avoid;
        box-sizing: border-box;
        overflow: hidden;
    }

    /* Header Section */
    .resume-container .header {
        text-align: left;
        margin-bottom: 25px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e0e0e0;
    }

    .resume-container .name {
        font-size: 22pt;
        font-weight: 300;
        color: #1a1a1a;
        letter-spacing: 1px;
        margin-bottom: 5px;
    }

    .resume-container .title {
        font-size: 11pt;
        color: #666666;
        margin-bottom: 8px;
        font-weight: 400;
    }

    .resume-container .contact-info {
        font-size: 9pt;
        color: #666666;
        line-height: 1.4;
    }

    /* Section Styling */
    .resume-container .section {
        margin-bottom: 20px;
    }

    .resume-container .section-title {
        font-size: 10pt;
        font-weight: 600;
        color: #1a1a1a;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 12px;
        padding-bottom: 3px;
        border-bottom: 1px solid #f0f0f0;
    }

    /* Experience and Education */
    .resume-container .experience-item,
    .resume-container .education-item {
        margin-bottom: 16px;
        padding-left: 0;
    }

    .resume-container .job-title,
    .resume-container .degree {
        font-size: 10pt;
        font-weight: 600;
        color: #1a1a1a;
        margin-bottom: 2px;
    }

    .resume-container .company,
    .resume-container .school {
        font-size: 10pt;
        color: #666666;
        margin-bottom: 3px;
        font-style: italic;
    }

    .resume-container .date {
        font-size: 9pt;
        color: #999999;
        margin-bottom: 6px;
        font-weight: 400;
    }

    .resume-container .description {
        font-size: 10pt;
        color: #2c2c2c;
        margin-left: 0;
    }

    .resume-container .description ul {
        margin-left: 16px;
        margin-top: 4px;
    }

    .resume-container .description li {
        margin-bottom: 3px;
        list-style-type: disc;
    }

    /* Skills Section */
    .resume-container .skills-list {
        font-size: 10pt;
        color: #2c2c2c;
    }

    .resume-container .skill-category {
        margin-bottom: 10px;
        line-height: 1.5;
    }

    .resume-container .skill-category strong {
        font-weight: 600;
        color: #1a1a1a;
    }

    /* Summary */
    .resume-container .summary {
        font-size: 10pt;
        color: #2c2c2c;
        line-height: 1.5;
        text-align: left;
    }

    /* Projects */
    .resume-container .project-item {
        margin-bottom: 12px;
    }

    .resume-container .project-title {
        font-weight: 600;
        color: #1a1a1a;
        margin-bottom: 3px;
    }

    .resume-container .project-description {
        color: #2c2c2c;
        margin-bottom: 2px;
    }

    .resume-container .project-tech {
        font-size: 9pt;
        color: #666666;
        font-style: italic;
    }

    /* Certifications */
    .resume-container .certification-item {
        margin-bottom: 6px;
        font-size: 10pt;
        color: #2c2c2c;
    }

    /* Print styles for A4 */
    @media print {
        .resume-container {
            width: 210mm !important;
            height: 297mm !important;
            margin: 0 !important;
            padding: 15mm 20mm !important;
            page-break-inside: avoid !important;
            box-sizing: border-box !important;
        }
    }

    /* PDF generation styles */
    @page {
        size: A4;
        margin: 0;
    }
</style>
<div class="resume-container">
    <!-- Header -->
    <div class="header">
        <div class="name">{{name}}</div>
        <div class="title">{{jobTitle}}</div>
        <div class="contact-info">
            {{joinContact email phone linkedin website github location ' • '}}
        </div>
    </div>

    <!-- Professional Summary -->
    {{#if summary}}
    <div class="section">
        <div class="section-title">Ringkasan Profesional</div>
        <div class="summary">
            {{summary}}
        </div>
    </div>
    {{/if}}

    <!-- Experience -->
    {{#if experiences}}
    <div class="section">
        <div class="section-title">Pengalaman Profesional</div>
        {{#each experiences}}
        <div class="experience-item">
            <div class="job-title">{{jobTitle}}</div>
            <div class="company">{{company}}</div>
            <div class="date">{{startDate}} - {{endDate}}</div>
            {{#if responsibilities}}
            <div class="description">
                <ul>
                    {{#each responsibilities}}
                    {{#if (hasContent this)}}<li>{{this}}</li>{{/if}}
                    {{/each}}
                </ul>
            </div>
            {{/if}}
        </div>
        {{/each}}
    </div>
    {{/if}}

    <!-- Education -->
    {{#if education}}
    <div class="section">
        <div class="section-title">Pendidikan</div>
        {{#each education}}
        <div class="education-item">
            <div class="degree">{{degree}}</div>
            <div class="school">{{institution}}</div>
            {{#if location}}<div class="school">{{location}}</div>{{/if}}
            <div class="date">{{graduationDate}}</div>
            {{#if gpa}}<div style="font-size: 9pt; color: #999999; margin-top: 2px;">IPK: {{gpa}}</div>{{/if}}
            {{#if relevantCoursework}}
            <div style="margin-top: 3px; font-size: 9pt; color: #666666;">
                <strong>Mata Kuliah Terkait:</strong> {{relevantCoursework}}
            </div>
            {{/if}}
            {{#if honors}}
            <div style="margin-top: 2px; font-size: 9pt; color: #666666;">
                <strong>Penghargaan:</strong> {{honors}}
            </div>
            {{/if}}
        </div>
        {{/each}}
    </div>
    {{/if}}

    <!-- Skills -->
    {{#if skills}}
    <div class="section">
        <div class="section-title">Keahlian</div>
        <div class="skills-list">
            {{#each skills}}
            {{#if (hasContent skills)}}
            <div class="skill-category">
                {{#if (hasContent category)}}<strong>{{category}}:</strong>{{/if}} {{skills}}
            </div>
            {{/if}}
            {{/each}}
        </div>
    </div>
    {{/if}}

    <!-- Projects -->
    {{#if projects}}
    <div class="section">
        <div class="section-title">Proyek Utama</div>
        {{#each projects}}
        <div class="project-item">
            <div class="project-title">{{title}}</div>
            {{#if link}}<div style="font-size: 9pt; color: #666666; margin-bottom: 2px;">{{link}}</div>{{/if}}
            {{#if description}}<div class="project-description">{{description}}</div>{{/if}}
            {{#if technologies}}<div class="project-tech">Teknologi: {{technologies}}</div>{{/if}}
            {{#if achievements}}
            <ul style="margin-left: 16px; margin-top: 3px; font-size: 10pt; color: #2c2c2c;">
                {{#each achievements}}
                {{#if (hasContent this)}}<li>{{this}}</li>{{/if}}
                {{/each}}
            </ul>
            {{/if}}
        </div>
        {{/each}}
    </div>
    {{/if}}

    <!-- Certifications -->
    {{#if certifications}}
    <div class="section">
        <div class="section-title">Sertifikasi</div>
        <div>
            {{#each certifications}}
            <div class="certification-item">
                {{name}} - {{issuer}} ({{date}})
                {{#if credentialId}}<div style="font-size: 9pt; color: #999999; margin-top: 1px;">ID: {{credentialId}}</div>{{/if}}
            </div>
            {{/each}}
        </div>
    </div>
    {{/if}}

    <!-- Languages -->
    {{#if languages}}
    <div class="section">
        <div class="section-title">Bahasa</div>
        <div>
            {{#each languages}}
            {{#if language}}
            <div class="certification-item">
                {{language}}{{#if proficiency}} - {{proficiency}}{{/if}}
            </div>
            {{/if}}
            {{/each}}
        </div>
    </div>
    {{/if}}

    <!-- Awards -->
    {{#if awards}}
    <div class="section">
        <div class="section-title">Penghargaan</div>
        <div>
            {{#each awards}}
            <div class="certification-item">
                <div style="font-weight: 600; color: #1a1a1a;">{{title}} - {{issuer}} ({{date}})</div>
                {{#if description}}<div style="font-size: 9pt; color: #666666; margin-top: 2px;">{{description}}</div>{{/if}}
            </div>
            {{/each}}
        </div>
    </div>
    {{/if}}
</div>
